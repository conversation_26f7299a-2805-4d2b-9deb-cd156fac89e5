/**
 * All of the CSS for your admin-specific functionality should be
 * included in this file.
 */

/* ==========================================================================
   MODERN ADMIN INTERFACE STYLES
   ========================================================================== */

/* Main Admin Wrapper */
.vicino-auction-admin-wrap {
    background: #f1f1f1;
    margin: 0 0px 0 -22px;
    padding: 0;
}

.vicino-auction-main-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0 0 0 0;
    padding: 30px 40px!important;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.vicino-auction-main-title .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.vicino-auction-admin-header {
    background: white;
    padding: 20px 40px;
    border-bottom: 1px solid #e1e1e1;
}

.vicino-auction-description {
    font-size: 16px;
    color: #666;
    margin: 0;
    line-height: 1.5;
}

/* Navigation Tabs */
.vicino-auction-nav-tabs {
    background: white;
    margin: 0;
    border-bottom: 1px solid #e1e1e1;
    padding: 0 40px;
}

.vicino-auction-nav-tabs .nav-tab {
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: #666;
    font-size: 14px;
    font-weight: 500;
    padding: 15px 20px;
    margin: 0 10px 0 0;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.vicino-auction-nav-tabs .nav-tab:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.vicino-auction-nav-tabs .nav-tab.nav-tab-active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.vicino-auction-nav-tabs .nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Tab Content */
.vicino-auction-tab-content {
    display: none;
    padding: 40px;
    background: #f1f1f1;
    min-height: 500px;
}

.vicino-auction-tab-content.active {
    display: block;
}

/* Settings Grid */
.vicino-auction-settings-grid {
    display: grid;
    gap: 30px;
    max-width: 1200px;
}

/* Cards */
.vicino-auction-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vicino-auction-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 30px rgba(0,0,0,0.12);
}

.vicino-auction-card-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    padding: 25px 30px;
    border-bottom: 1px solid #e1e1e1;
}

.vicino-auction-card-header h2 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.vicino-auction-card-header h2 .dashicons {
    color: #667eea;
    font-size: 22px;
    width: 22px;
    height: 22px;
}

.vicino-auction-card-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* Form Styles */
.vicino-auction-form {
    padding: 30px;
}

.vicino-auction-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.vicino-auction-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.vicino-auction-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vicino-auction-label .dashicons {
    color: #667eea;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.vicino-auction-input {
    padding: 12px 16px;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.vicino-auction-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.vicino-auction-help-text {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Tooltips */
.vicino-auction-tooltip {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    cursor: help;
    margin-left: 5px;
}

.vicino-auction-tooltip-content {
    position: absolute;
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    max-width: 250px;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.vicino-auction-tooltip-content::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 10px;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #333;
}

/* Form Actions */
.vicino-auction-form-actions {
    padding: 0 30px 30px;
}

.vicino-auction-save-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vicino-auction-save-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Danger Card */
.vicino-auction-danger-card {
    border-left: 4px solid #dc3545;
}

.vicino-auction-danger-card .vicino-auction-card-header {
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
}

.vicino-auction-danger-card .vicino-auction-card-header h2 .dashicons {
    color: #dc3545;
}

/* Reset Section */
.vicino-auction-reset-section {
    padding: 30px;
}

.vicino-auction-reset-section h3 {
    color: #dc3545;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.vicino-auction-warning-text {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    color: #856404;
    margin: 15px 0;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    font-size: 14px;
    line-height: 1.5;
}

.vicino-auction-warning-text .dashicons {
    color: #f39c12;
    margin-top: 2px;
    flex-shrink: 0;
}

.vicino-auction-reset-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 20px;
}

.vicino-auction-danger-button {
    background: #dc3545;
    border: 1px solid #dc3545;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vicino-auction-danger-button:hover {
    background: #c82333;
    border-color: #c82333;
    transform: translateY(-1px);
}

.vicino-auction-danger-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.vicino-auction-status-message {
    font-size: 14px;
    font-weight: 500;
    padding: 8px 0;
}

/* Legacy Styles (keeping for compatibility) */
.vicino-auction-meta-box {
    padding: 10px;
}

.vicino-auction-meta-box label {
    font-weight: bold;
}

.vicino-auction-meta-box input[type="number"],
.vicino-auction-meta-box input[type="datetime-local"] {
    width: 100%;
    max-width: 300px;
}

.status-active {
    color: green;
    font-weight: bold;
}

.status-inactive {
    color: red;
}

.export-buttons {
    margin: 20px 0;
}

.export-buttons button {
    margin-right: 10px;
}

/* ==========================================================================
   SHORTCODES DOCUMENTATION STYLES
   ========================================================================== */

.vicino-auction-shortcodes-docs {
    max-width: 1200px;
}

.vicino-auction-docs-header {
    background: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
}

.vicino-auction-docs-header h2 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 12px;
}

.vicino-auction-docs-header h2 .dashicons {
    color: #667eea;
    font-size: 28px;
    width: 28px;
    height: 28px;
}

.vicino-auction-docs-description {
    font-size: 16px;
    color: #666;
    margin: 0;
    line-height: 1.6;
}

.vicino-auction-shortcodes-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
}

/* Shortcode Cards */
.vicino-auction-shortcode-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.vicino-auction-shortcode-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 30px rgba(0,0,0,0.12);
}

.vicino-auction-shortcode-header {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
    padding: 20px 25px;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vicino-auction-shortcode-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Courier New', monospace;
}

.vicino-auction-shortcode-header h3 .dashicons {
    color: #667eea;
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.vicino-auction-shortcode-badge {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vicino-auction-badge-status {
    background: #28a745;
}

.vicino-auction-badge-list {
    background: #17a2b8;
}

.vicino-auction-badge-table {
    background: #6f42c1;
}

.vicino-auction-badge-export {
    background: #fd7e14;
}

.vicino-auction-shortcode-content {
    padding: 25px;
}

.vicino-auction-shortcode-description {
    font-size: 15px;
    color: #555;
    line-height: 1.6;
    margin: 0 0 20px 0;
}

.vicino-auction-shortcode-params h4,
.vicino-auction-shortcode-examples h4 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 20px 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vicino-auction-shortcode-params h4::before {
    content: "⚙️";
    font-size: 14px;
}

.vicino-auction-shortcode-examples h4::before {
    content: "💡";
    font-size: 14px;
}

.vicino-auction-shortcode-params ul {
    margin: 0;
    padding-left: 20px;
}

.vicino-auction-shortcode-params li {
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.5;
}

.vicino-auction-shortcode-params code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #e83e8c;
    font-weight: 600;
}

.vicino-auction-no-params {
    font-style: italic;
    color: #666;
    margin: 0;
    font-size: 14px;
}

/* Code Blocks */
.vicino-auction-code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.vicino-auction-code-block:hover {
    background: #e9ecef;
}

.vicino-auction-code-block code {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
    font-weight: 600;
    background: none;
    padding: 0;
}

.vicino-auction-copy-btn {
    background: #667eea;
    border: none;
    color: white;
    padding: 6px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vicino-auction-copy-btn:hover {
    background: #5a67d8;
    transform: scale(1.05);
}

.vicino-auction-copy-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Status Preview */
.vicino-auction-shortcode-preview {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.vicino-auction-shortcode-preview h4 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.vicino-auction-status-examples {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.vicino-auction-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vicino-status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.vicino-status-has-bids {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.vicino-status-finished {
    background: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.vicino-auction-shortcode-note {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
}

.vicino-auction-shortcode-note p {
    margin: 0;
    font-size: 14px;
    color: #004085;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.vicino-auction-shortcode-note .dashicons {
    color: #0066cc;
    margin-top: 2px;
    flex-shrink: 0;
}

/* Tips Section */
.vicino-auction-tips-section {
    margin-top: 40px;
}

.vicino-auction-tips-content {
    padding: 30px;
}

.vicino-auction-tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.vicino-auction-tip {
    background: #f8f9ff;
    border: 1px solid #e8ecff;
    border-radius: 8px;
    padding: 20px;
    transition: transform 0.2s ease;
}

.vicino-auction-tip:hover {
    transform: translateY(-2px);
}

.vicino-auction-tip h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.vicino-auction-tip h4 .dashicons {
    color: #667eea;
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.vicino-auction-tip p {
    margin: 0;
    font-size: 14px;
    color: #555;
    line-height: 1.5;
}

/* ==========================================================================
   HELP TAB STYLES
   ========================================================================== */

.vicino-auction-help-grid {
    display: grid;
    gap: 30px;
    max-width: 1000px;
}

.vicino-auction-help-content {
    padding: 30px;
}

.vicino-auction-steps {
    margin: 0;
    padding-left: 20px;
}

.vicino-auction-steps li {
    margin-bottom: 15px;
    font-size: 15px;
    line-height: 1.6;
    color: #555;
}

.vicino-auction-steps strong {
    color: #333;
}

.vicino-auction-support-links {
    display: grid;
    gap: 20px;
}

.vicino-auction-support-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #f8f9ff;
    border-radius: 8px;
    border: 1px solid #e8ecff;
}

.vicino-auction-support-item .dashicons {
    color: #667eea;
    font-size: 24px;
    width: 24px;
    height: 24px;
    margin-top: 2px;
    flex-shrink: 0;
}

.vicino-auction-support-item h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.vicino-auction-support-item p {
    margin: 0;
    font-size: 14px;
    color: #555;
    line-height: 1.5;
}

.vicino-auction-info-table {
    width: 100%;
    border-collapse: collapse;
}

.vicino-auction-info-table td {
    padding: 12px 0;
    border-bottom: 1px solid #e1e1e1;
    font-size: 14px;
}

.vicino-auction-info-table td:first-child {
    width: 40%;
    color: #666;
}

.vicino-auction-info-table td:last-child {
    color: #333;
    font-weight: 500;
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 768px) {
    .vicino-auction-main-title {
        padding: 20px;
        font-size: 24px;
    }

    .vicino-auction-admin-header,
    .vicino-auction-nav-tabs,
    .vicino-auction-tab-content {
        padding-left: 20px;
        padding-right: 20px;
    }

    .vicino-auction-form-grid {
        grid-template-columns: 1fr;
    }

    .vicino-auction-nav-tabs .nav-tab {
        padding: 12px 15px;
        margin-right: 5px;
        font-size: 13px;
    }

    .vicino-auction-tips-grid {
        grid-template-columns: 1fr;
    }

    .vicino-auction-status-examples {
        flex-direction: column;
        align-items: flex-start;
    }

    .vicino-auction-code-block {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .vicino-auction-copy-btn {
        align-self: flex-end;
    }
}

#vicino-auction-reset-button {
    display: flex;
    background-color: #d63638;
    border-color: #d63638;
    color: white;
}

#vicino-auction-reset-button:hover {
    background-color: #b32d2e;
    border-color: #b32d2e;
}

#vicino-auction-reset-status {
    font-weight: bold;
}

.update-nag.notice.notice-warning.inline {
    display: none;
}
.notice.notice-success.settings-error {
    display: none;
}