(function( $ ) {
	'use strict';

	/**
	 * All of the JavaScript for your admin-specific functionality should be
	 * included in this file.
	 */

	/**
	 * Initialize admin functionality when the DOM is ready
	 */
	$(document).ready(function() {
		// Handle bid deletion
		$('.delete-bid').on('click', function(e) {
			e.preventDefault();

			if (!confirm(vicino_auction_admin.confirm_delete_message)) {
				return false;
			}

			var bidId = $(this).data('bid-id');
			var nonce = $(this).data('nonce');
			var row = $(this).closest('tr');

			$.ajax({
				url: vicino_auction_admin.ajax_url,
				type: 'POST',
				data: {
					action: 'vicino_auction_delete_bid',
					bid_id: bidId,
					nonce: nonce
				},
				success: function(response) {
					if (response.success) {
						row.fadeOut(300, function() {
							$(this).remove();

							// If no more rows, show the empty message
							if ($('.wp-list-table tbody tr').length === 0) {
								$('.wp-list-table').replaceWith('<p>' + vicino_auction_admin.no_bids_message + '</p>');
							}
						});
					} else {
						alert(response.data);
					}
				},
				error: function() {
					alert(vicino_auction_admin.error_message);
				}
			});
		});

		// Handle auction reset
		$('#vicino-auction-reset-button').on('click', function(e) {
			e.preventDefault();

			// Show confirmation dialog
			if (!confirm(vicino_auction_admin.confirm_reset_message || '¿Está seguro de que desea reiniciar la subasta? Esta acción eliminará todas las ofertas y no se puede deshacer.')) {
				return false;
			}

			var $button = $(this);
			var $status = $('#vicino-auction-reset-status');
			var nonce = $button.data('nonce');

			// Disable button and show loading message
			$button.prop('disabled', true);
			$status.text('Procesando...').show();

			// Send AJAX request to reset auction
			$.ajax({
				url: vicino_auction_admin.ajax_url,
				type: 'POST',
				data: {
					action: 'vicino_auction_reset',
					nonce: nonce
				},
				success: function(response) {
					if (response.success) {
						$status.text('¡Subasta reiniciada correctamente!').css('color', 'green');
						setTimeout(function() {
							location.reload();
						}, 2000);
					} else {
						$button.prop('disabled', false);
						$status.text('Error: ' + (response.data || 'Ocurrió un error desconocido')).css('color', 'red');
					}
				},
				error: function() {
					$button.prop('disabled', false);
					$status.text('Error: No se pudo conectar con el servidor').css('color', 'red');
				}
			});
		});

		// Copy to clipboard functionality for shortcodes
		$('.vicino-auction-copy-btn').on('click', function(e) {
			e.preventDefault();

			var textToCopy = $(this).data('clipboard-text');
			var $button = $(this);
			var originalIcon = $button.find('.dashicons').attr('class');

			// Create a temporary textarea to copy text
			var $temp = $('<textarea>');
			$('body').append($temp);
			$temp.val(textToCopy).select();

			try {
				document.execCommand('copy');

				// Show success feedback
				$button.find('.dashicons').removeClass().addClass('dashicons dashicons-yes');
				$button.css('background', '#28a745');

				setTimeout(function() {
					$button.find('.dashicons').removeClass().addClass(originalIcon);
					$button.css('background', '');
				}, 2000);

			} catch (err) {
				console.error('Error copying to clipboard:', err);

				// Show error feedback
				$button.find('.dashicons').removeClass().addClass('dashicons dashicons-no');
				$button.css('background', '#dc3545');

				setTimeout(function() {
					$button.find('.dashicons').removeClass().addClass(originalIcon);
					$button.css('background', '');
				}, 2000);
			}

			$temp.remove();
		});

		// Enhanced form validation
		$('#vicino_auction_start_date, #vicino_auction_end_date').on('change', function() {
			var startDate = new Date($('#vicino_auction_start_date').val());
			var endDate = new Date($('#vicino_auction_end_date').val());

			if (startDate && endDate && startDate >= endDate) {
				$(this).css('border-color', '#dc3545');

				// Show validation message
				var $message = $(this).siblings('.vicino-auction-validation-message');
				if ($message.length === 0) {
					$message = $('<div class="vicino-auction-validation-message"></div>');
					$(this).after($message);
				}

				$message.text('La fecha de finalización debe ser posterior a la fecha de inicio.')
					.css({
						'color': '#dc3545',
						'font-size': '13px',
						'margin-top': '5px'
					});
			} else {
				$(this).css('border-color', '');
				$(this).siblings('.vicino-auction-validation-message').remove();
			}
		});

		// Auto-save draft functionality (optional enhancement)
		var autoSaveTimeout;
		$('.vicino-auction-input').on('input', function() {
			clearTimeout(autoSaveTimeout);
			autoSaveTimeout = setTimeout(function() {
				// Could implement auto-save functionality here
				console.log('Auto-save triggered');
			}, 2000);
		});

		// Smooth scrolling for anchor links
		$('a[href^="#"]').on('click', function(e) {
			var target = $(this.getAttribute('href'));
			if (target.length) {
				e.preventDefault();
				$('html, body').stop().animate({
					scrollTop: target.offset().top - 100
				}, 500);
			}
		});

		// Add loading states to buttons
		$('.vicino-auction-save-button').on('click', function() {
			var $button = $(this);
			var originalText = $button.text();

			$button.prop('disabled', true)
				.text('Guardando...')
				.css('opacity', '0.7');

			// Re-enable after form submission (this is handled by the browser)
			setTimeout(function() {
				$button.prop('disabled', false)
					.text(originalText)
					.css('opacity', '');
			}, 3000);
		});

		// Keyboard shortcuts
		$(document).on('keydown', function(e) {
			// Ctrl/Cmd + S to save
			if ((e.ctrlKey || e.metaKey) && e.which === 83) {
				e.preventDefault();
				$('.vicino-auction-save-button').trigger('click');
			}

			// Ctrl/Cmd + 1, 2, 3 to switch tabs
			if ((e.ctrlKey || e.metaKey) && e.which >= 49 && e.which <= 51) {
				e.preventDefault();
				var tabIndex = e.which - 49;
				$('.vicino-auction-nav-tabs .nav-tab').eq(tabIndex).trigger('click');
			}
		});

		// Add tooltips for better UX
		$('.vicino-auction-tooltip').each(function() {
			var $tooltip = $(this);
			var title = $tooltip.attr('title');

			if (title) {
				$tooltip.removeAttr('title').on('mouseenter', function(e) {
					var $tooltipContent = $('<div class="vicino-auction-tooltip-content">' + title + '</div>');
					$('body').append($tooltipContent);

					$tooltipContent.css({
						position: 'absolute',
						top: e.pageY + 10,
						left: e.pageX + 10,
						zIndex: 1000
					}).fadeIn(200);

				}).on('mouseleave', function() {
					$('.vicino-auction-tooltip-content').fadeOut(200, function() {
						$(this).remove();
					});

				}).on('mousemove', function(e) {
					$('.vicino-auction-tooltip-content').css({
						top: e.pageY + 10,
						left: e.pageX + 10
					});
				});
			}
		});

		// Add confirmation for dangerous actions
		$('.vicino-auction-danger-button').on('click', function(e) {
			if (!$(this).hasClass('confirmed')) {
				e.preventDefault();

				var $button = $(this);
				var originalText = $button.text();
				var originalClass = $button.attr('class');

				$button.text('¿Está seguro? Haga clic nuevamente para confirmar')
					.addClass('confirmed')
					.css('background', '#ffc107');

				setTimeout(function() {
					$button.text(originalText)
						.removeClass('confirmed')
						.attr('class', originalClass);
				}, 5000);
			}
		});
	});

})( jQuery );