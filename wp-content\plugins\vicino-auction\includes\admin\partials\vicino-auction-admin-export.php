<?php

/**
 * Provide a admin area view for exporting auction data
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="card">
        <h2><?php _e('Exportar Datos de la Subasta', 'vicino-auction'); ?></h2>
        <p><?php _e('Utilice los botones a continuación para exportar los datos de la subasta en formato Excel.', 'vicino-auction'); ?></p>
        
        <div class="export-buttons">
            <button type="button" class="button button-primary" data-export-type="all">
                <?php _e('Exportar Todos los Datos', 'vicino-auction'); ?>
            </button>
            
            <button type="button" class="button" data-export-type="winners">
                <?php _e('Exportar Solo Ganadores', 'vicino-auction'); ?>
            </button>
        </div>
        
        <div id="export-status" style="margin-top: 15px; display: none;">
            <span class="spinner is-active" style="float: none;"></span>
            <span class="status-text"><?php _e('Generando archivo de exportación...', 'vicino-auction'); ?></span>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        $('.export-buttons button').on('click', function() {
            var exportType = $(this).data('export-type');
            var $status = $('#export-status');
            
            $status.show();
            
            $.ajax({
                url: vicino_auction_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'vicino_auction_export_excel',
                    export_type: exportType,
                    nonce: vicino_auction_admin.nonce
                },
                success: function(response) {
                    $status.hide();
                    
                    if (response.success && response.data.file_url) {
                        window.location.href = response.data.file_url;
                    } else {
                        alert('Error en la exportación: ' + (response.data || 'Error desconocido'));
                    }
                },
                error: function() {
                    $status.hide();
                    alert('La exportación falló debido a un error del servidor.');
                }
            });
        });
    });
    </script>
</div>