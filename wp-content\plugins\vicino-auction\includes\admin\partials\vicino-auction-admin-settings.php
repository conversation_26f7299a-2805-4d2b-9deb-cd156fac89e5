<?php

/**
 * Provide a admin area view for the plugin settings
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap vicino-auction-admin-wrap">
    <h1 class="vicino-auction-main-title">
        <span class="dashicons dashicons-hammer"></span>
        <?php echo esc_html(get_admin_page_title()); ?>
    </h1>

    <div class="vicino-auction-admin-header">
        <p class="vicino-auction-description">
            <?php _e('Configure los parámetros de su subasta y consulte la documentación de shortcodes disponibles.', 'vicino-auction'); ?>
        </p>
    </div>

    <!-- Navigation Tabs -->
    <nav class="nav-tab-wrapper vicino-auction-nav-tabs">
        <a href="#general" class="nav-tab nav-tab-active" data-tab="general">
            <span class="dashicons dashicons-admin-settings"></span>
            <?php _e('Configuración General', 'vicino-auction'); ?>
        </a>
        <a href="#shortcodes" class="nav-tab" data-tab="shortcodes">
            <span class="dashicons dashicons-shortcode"></span>
            <?php _e('Documentación de Shortcodes', 'vicino-auction'); ?>
        </a>
        <a href="#help" class="nav-tab" data-tab="help">
            <span class="dashicons dashicons-sos"></span>
            <?php _e('Ayuda y Soporte', 'vicino-auction'); ?>
        </a>
    </nav>

    <!-- General Settings Tab -->
    <div id="general-tab" class="vicino-auction-tab-content active">
        <div class="vicino-auction-settings-grid">

            <!-- Auction Configuration Card -->
            <div class="vicino-auction-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-calendar-alt"></span>
                        <?php _e('Configuración de Fechas', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Configure las fechas de inicio y finalización de la subasta.', 'vicino-auction'); ?></p>
                </div>

                <form method="post" action="options.php" class="vicino-auction-form">
                    <?php
                    settings_fields('vicino_auction_settings');
                    do_settings_sections('vicino_auction_settings');
                    ?>

                    <div class="vicino-auction-form-grid">
                        <div class="vicino-auction-form-group">
                            <label for="vicino_auction_start_date" class="vicino-auction-label">
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('Fecha de Inicio de la Subasta', 'vicino-auction'); ?>
                                <span class="vicino-auction-tooltip" title="<?php _e('La fecha y hora cuando comenzará la subasta.', 'vicino-auction'); ?>">?</span>
                            </label>
                            <input type="datetime-local"
                                   id="vicino_auction_start_date"
                                   name="vicino_auction_start_date"
                                   value="<?php
                                   $start_date = get_option('vicino_auction_start_date');
                                   if (!empty($start_date)) {
                                       // Convert UTC stored date to local timezone for display only
                                       try {
                                           $timezone_string = function_exists('wp_timezone_string') ? wp_timezone_string() : get_option('timezone_string', 'UTC');
                                           if (empty($timezone_string)) {
                                               $timezone_string = 'UTC';
                                           }
                                           $date = new DateTime($start_date, new DateTimeZone('UTC'));
                                           $date->setTimezone(new DateTimeZone($timezone_string));
                                           echo esc_attr($date->format('Y-m-d\TH:i'));
                                       } catch (Exception $e) {
                                           echo esc_attr(str_replace(' ', 'T', $start_date));
                                       }
                                   }
                                   ?>"
                                   class="vicino-auction-input" />
                            <p class="vicino-auction-help-text"><?php _e('La fecha y hora cuando comenzará la subasta.', 'vicino-auction'); ?></p>
                        </div>

                        <div class="vicino-auction-form-group">
                            <label for="vicino_auction_end_date" class="vicino-auction-label">
                                <span class="dashicons dashicons-clock"></span>
                                <?php _e('Fecha de Finalización de la Subasta', 'vicino-auction'); ?>
                                <span class="vicino-auction-tooltip" title="<?php _e('La fecha y hora cuando finalizará la subasta.', 'vicino-auction'); ?>">?</span>
                            </label>
                            <input type="datetime-local"
                                   id="vicino_auction_end_date"
                                   name="vicino_auction_end_date"
                                   value="<?php
                                   $end_date = get_option('vicino_auction_end_date');
                                   if (!empty($end_date)) {
                                       // Convert UTC stored date to local timezone for display only
                                       try {
                                           $timezone_string = function_exists('wp_timezone_string') ? wp_timezone_string() : get_option('timezone_string', 'UTC');
                                           if (empty($timezone_string)) {
                                               $timezone_string = 'UTC';
                                           }
                                           $date = new DateTime($end_date, new DateTimeZone('UTC'));
                                           $date->setTimezone(new DateTimeZone($timezone_string));
                                           echo esc_attr($date->format('Y-m-d\TH:i'));
                                       } catch (Exception $e) {
                                           echo esc_attr(str_replace(' ', 'T', $end_date));
                                       }
                                   }
                                   ?>"
                                   class="vicino-auction-input" />
                            <p class="vicino-auction-help-text"><?php _e('La fecha y hora cuando finalizará la subasta.', 'vicino-auction'); ?></p>
                        </div>
                    </div>
            </div>

            <!-- Bidding Rules Card -->
            <div class="vicino-auction-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-shield"></span>
                        <?php _e('Reglas de Preoferta', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Configure las reglas para las preofertas y protección contra remates.', 'vicino-auction'); ?></p>
                </div>

                <div class="vicino-auction-form-grid vicino-auction-form">
                    <div class="vicino-auction-form-group">
                        <label for="vicino_auction_sniping_minutes" class="vicino-auction-label">
                            <span class="dashicons dashicons-shield-alt"></span>
                            <?php _e('Minutos de Protección contra Remates', 'vicino-auction'); ?>
                            <span class="vicino-auction-tooltip" title="<?php _e('Cantidad de minutos antes del final de la subasta cuando se activa la protección contra remates.', 'vicino-auction'); ?>">?</span>
                        </label>
                        <input type="number"
                               id="vicino_auction_sniping_minutes"
                               name="vicino_auction_sniping_minutes"
                               value="<?php echo esc_attr(get_option('vicino_auction_sniping_minutes', 5)); ?>"
                               min="0" max="60"
                               class="vicino-auction-input" />
                        <p class="vicino-auction-help-text"><?php _e('Cantidad de minutos antes del final de la subasta cuando se activa la protección contra remates. Usar 0 para desactivar esta funcionalidad.', 'vicino-auction'); ?></p>
                    </div>

                    <div class="vicino-auction-form-group">
                        <label for="vicino_auction_sniping_extension" class="vicino-auction-label">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Minutos de Extensión de Tiempo', 'vicino-auction'); ?>
                            <span class="vicino-auction-tooltip" title="<?php _e('Cantidad de minutos para extender la subasta cuando se realiza una Preoferta durante el período de protección contra remates.', 'vicino-auction'); ?>">?</span>
                        </label>
                        <input type="number"
                               id="vicino_auction_sniping_extension"
                               name="vicino_auction_sniping_extension"
                               value="<?php echo esc_attr(get_option('vicino_auction_sniping_extension', 10)); ?>"
                               min="0" max="60"
                               class="vicino-auction-input" />
                        <p class="vicino-auction-help-text"><?php _e('Cantidad de minutos para extender la subasta cuando se realiza una Preoferta durante el período de protección contra remates. Usar 0 para desactivar esta funcionalidad.', 'vicino-auction'); ?></p>
                    </div>

                    <div class="vicino-auction-form-group">
                        <label for="vicino_auction_min_increment" class="vicino-auction-label">
                            <span class="dashicons dashicons-arrow-up-alt"></span>
                            <?php _e('Incremento Mínimo de Preoferta', 'vicino-auction'); ?>
                            <span class="vicino-auction-tooltip" title="<?php _e('Monto mínimo que cada nueva Preoferta debe superar a la preoferta actual.', 'vicino-auction'); ?>">?</span>
                        </label>
                        <input type="number"
                               id="vicino_auction_min_increment"
                               name="vicino_auction_min_increment"
                               value="<?php echo esc_attr(get_option('vicino_auction_min_increment', 1)); ?>"
                               min="1" step="1"
                               class="vicino-auction-input" />
                        <p class="vicino-auction-help-text"><?php _e('Monto mínimo que cada nueva Preoferta debe superar a la preoferta actual.', 'vicino-auction'); ?></p>
                    </div>
                </div>
            </div>

            <div class="vicino-auction-form-actions">
                <?php submit_button(__('Guardar Configuración', 'vicino-auction'), 'primary large', 'submit', false, array('class' => 'vicino-auction-save-button')); ?>
            </div>

            </form>

            <!-- Reset Section Card -->
            <div class="vicino-auction-card vicino-auction-danger-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Zona de Peligro', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Acciones irreversibles que afectarán permanentemente su subasta.', 'vicino-auction'); ?></p>
                </div>

                <div class="vicino-auction-reset-section">
                    <h3><?php _e('Reiniciar Subasta', 'vicino-auction'); ?></h3>
                    <p class="vicino-auction-warning-text">
                        <span class="dashicons dashicons-warning"></span>
                        <?php _e('Esta acción eliminará todas las Preofertas y reiniciará la información de la subasta. Esta acción no se puede deshacer.', 'vicino-auction'); ?>
                    </p>

                    <div class="vicino-auction-reset-actions">
                        <button type="button" id="vicino-auction-reset-button" class="button button-secondary button-large vicino-auction-danger-button" data-nonce="<?php echo wp_create_nonce('vicino_auction_reset_nonce'); ?>">
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Reiniciar Subasta', 'vicino-auction'); ?>
                        </button>
                        <span id="vicino-auction-reset-status" class="vicino-auction-status-message"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shortcodes Documentation Tab -->
    <div id="shortcodes-tab" class="vicino-auction-tab-content">
        <?php include_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/partials/vicino-auction-admin-shortcodes-docs.php'; ?>
    </div>

    <!-- Help and Support Tab -->
    <div id="help-tab" class="vicino-auction-tab-content">
        <div class="vicino-auction-help-grid">

            <!-- Quick Start Card -->
            <div class="vicino-auction-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-lightbulb"></span>
                        <?php _e('Inicio Rápido', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Pasos básicos para configurar su primera subasta.', 'vicino-auction'); ?></p>
                </div>

                <div class="vicino-auction-help-content">
                    <ol class="vicino-auction-steps">
                        <li>
                            <strong><?php _e('Configure las fechas:', 'vicino-auction'); ?></strong>
                            <?php _e('Establezca la fecha de inicio y finalización de su subasta en la pestaña "Configuración General".', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <strong><?php _e('Cree lotes de subasta:', 'vicino-auction'); ?></strong>
                            <?php _e('Vaya a "Lotes de Subasta" y cree nuevos posts habilitando la opción de subasta.', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <strong><?php _e('Configure precios iniciales:', 'vicino-auction'); ?></strong>
                            <?php _e('Establezca el precio inicial para cada lote en la configuración del post.', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <strong><?php _e('Agregue shortcodes:', 'vicino-auction'); ?></strong>
                            <?php _e('Use los shortcodes disponibles para mostrar las subastas en su sitio web.', 'vicino-auction'); ?>
                        </li>
                    </ol>
                </div>
            </div>

            <!-- Support Card -->
            <div class="vicino-auction-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-sos"></span>
                        <?php _e('Soporte y Ayuda', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Recursos adicionales y información de contacto.', 'vicino-auction'); ?></p>
                </div>

                <div class="vicino-auction-help-content">
                    <div class="vicino-auction-support-links">
                        <div class="vicino-auction-support-item">
                            <span class="dashicons dashicons-book"></span>
                            <div>
                                <h4><?php _e('Documentación', 'vicino-auction'); ?></h4>
                                <p><?php _e('Consulte la pestaña "Documentación de Shortcodes" para obtener información detallada sobre todos los shortcodes disponibles.', 'vicino-auction'); ?></p>
                            </div>
                        </div>

                        <div class="vicino-auction-support-item">
                            <span class="dashicons dashicons-admin-tools"></span>
                            <div>
                                <h4><?php _e('Configuración Avanzada', 'vicino-auction'); ?></h4>
                                <p><?php _e('Ajuste los parámetros de protección contra remates y incrementos mínimos según sus necesidades.', 'vicino-auction'); ?></p>
                            </div>
                        </div>

                        <div class="vicino-auction-support-item">
                            <span class="dashicons dashicons-backup"></span>
                            <div>
                                <h4><?php _e('Exportación de Datos', 'vicino-auction'); ?></h4>
                                <p><?php _e('Use la página "Exportar" para descargar los resultados de su subasta en formato Excel.', 'vicino-auction'); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Plugin Info Card -->
            <div class="vicino-auction-card">
                <div class="vicino-auction-card-header">
                    <h2>
                        <span class="dashicons dashicons-info"></span>
                        <?php _e('Información del Plugin', 'vicino-auction'); ?>
                    </h2>
                    <p><?php _e('Detalles técnicos y versión del plugin.', 'vicino-auction'); ?></p>
                </div>

                <div class="vicino-auction-help-content">
                    <table class="vicino-auction-info-table">
                        <tr>
                            <td><strong><?php _e('Nombre del Plugin:', 'vicino-auction'); ?></strong></td>
                            <td>Vicino Auction</td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Versión:', 'vicino-auction'); ?></strong></td>
                            <td>2.0.1</td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Autor:', 'vicino-auction'); ?></strong></td>
                            <td><a href="https://vicinoweb.com" target="_blank">Vicino Web</a></td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Compatibilidad WordPress:', 'vicino-auction'); ?></strong></td>
                            <td>5.0+</td>
                        </tr>
                        <tr>
                            <td><strong><?php _e('Compatibilidad PHP:', 'vicino-auction'); ?></strong></td>
                            <td>7.4+</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Tab switching functionality
    $('.vicino-auction-nav-tabs .nav-tab').on('click', function(e) {
        e.preventDefault();

        var targetTab = $(this).data('tab');

        // Remove active class from all tabs and content
        $('.nav-tab').removeClass('nav-tab-active');
        $('.vicino-auction-tab-content').removeClass('active');

        // Add active class to clicked tab and corresponding content
        $(this).addClass('nav-tab-active');
        $('#' + targetTab + '-tab').addClass('active');

        // Update URL hash without scrolling
        if (history.pushState) {
            history.pushState(null, null, '#' + targetTab);
        }
    });

    // Check for hash in URL on page load
    var hash = window.location.hash.substring(1);
    if (hash && $('#' + hash + '-tab').length) {
        $('.nav-tab[data-tab="' + hash + '"]').trigger('click');
    }

    // Enhanced tooltips
    $('.vicino-auction-tooltip').hover(
        function() {
            var title = $(this).attr('title');
            $(this).data('tipText', title).removeAttr('title');
            $('<div class="vicino-auction-tooltip-content">' + title + '</div>')
                .appendTo('body')
                .fadeIn('fast');
        },
        function() {
            $(this).attr('title', $(this).data('tipText'));
            $('.vicino-auction-tooltip-content').remove();
        }
    ).mousemove(function(e) {
        $('.vicino-auction-tooltip-content').css({
            top: e.pageY + 10,
            left: e.pageX + 10
        });
    });
});
</script>