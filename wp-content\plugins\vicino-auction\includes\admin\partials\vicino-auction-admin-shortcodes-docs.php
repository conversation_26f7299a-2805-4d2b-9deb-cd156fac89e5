<?php

/**
 * Provide a admin area view for the shortcodes documentation
 *
 * @since      1.0.0
 */

// If this file is called directly, abort.
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="vicino-auction-shortcodes-docs">
    <div class="vicino-auction-docs-header">
        <h2>
            <span class="dashicons dashicons-shortcode"></span>
            <?php _e('Documentación de Shortcodes', 'vicino-auction'); ?>
        </h2>
        <p class="vicino-auction-docs-description">
            <?php _e('Utilice estos shortcodes para mostrar las funcionalidades de subasta en su sitio web. Copie y pegue el código en cualquier página o post.', 'vicino-auction'); ?>
        </p>
    </div>

    <div class="vicino-auction-shortcodes-grid">
        
        <!-- Shortcode 1: vicino_auction -->
        <div class="vicino-auction-shortcode-card">
            <div class="vicino-auction-shortcode-header">
                <h3>
                    <span class="dashicons dashicons-hammer"></span>
                    [vicino_auction]
                </h3>
                <span class="vicino-auction-shortcode-badge"><?php _e('Principal', 'vicino-auction'); ?></span>
            </div>
            
            <div class="vicino-auction-shortcode-content">
                <p class="vicino-auction-shortcode-description">
                    <?php _e('Muestra el formulario completo de subasta para un lote específico, incluyendo información del lote, preoferta actual, formulario para realizar preofertas y historial de preofertas.', 'vicino-auction'); ?>
                </p>
                
                <div class="vicino-auction-shortcode-params">
                    <h4><?php _e('Parámetros:', 'vicino-auction'); ?></h4>
                    <ul>
                        <li>
                            <code>id</code> - <?php _e('ID del lote de subasta (opcional, por defecto usa el ID del post actual)', 'vicino-auction'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="vicino-auction-shortcode-examples">
                    <h4><?php _e('Ejemplos de uso:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction id="123"]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction id=&quot;123&quot;]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shortcode 2: vicino_auction_status -->
        <div class="vicino-auction-shortcode-card">
            <div class="vicino-auction-shortcode-header">
                <h3>
                    <span class="dashicons dashicons-flag"></span>
                    [vicino_auction_status]
                </h3>
                <span class="vicino-auction-shortcode-badge vicino-auction-badge-status"><?php _e('Estado', 'vicino-auction'); ?></span>
            </div>
            
            <div class="vicino-auction-shortcode-content">
                <p class="vicino-auction-shortcode-description">
                    <?php _e('Muestra el estado actual de un lote de subasta como una insignia colorida. Los estados incluyen: "SUBASTA INICIADA" (verde), "PREOFERTADO" (rojo), "SUBASTA FINALIZADA" (gris).', 'vicino-auction'); ?>
                </p>
                
                <div class="vicino-auction-shortcode-params">
                    <h4><?php _e('Parámetros:', 'vicino-auction'); ?></h4>
                    <ul>
                        <li>
                            <code>id</code> - <?php _e('ID del lote de subasta (opcional, por defecto usa el ID del post actual)', 'vicino-auction'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="vicino-auction-shortcode-examples">
                    <h4><?php _e('Ejemplos de uso:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_status]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_status]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_status id="456"]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_status id=&quot;456&quot;]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                </div>
                
                <div class="vicino-auction-shortcode-preview">
                    <h4><?php _e('Vista previa de estados:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-status-examples">
                        <div class="vicino-auction-status-badge vicino-status-active">
                            <span class="status-icon">🟢</span>
                            <span class="status-text"><?php _e('SUBASTA INICIADA', 'vicino-auction'); ?></span>
                        </div>
                        <div class="vicino-auction-status-badge vicino-status-has-bids">
                            <span class="status-icon">🔨</span>
                            <span class="status-text"><?php _e('PREOFERTADO', 'vicino-auction'); ?></span>
                        </div>
                        <div class="vicino-auction-status-badge vicino-status-finished">
                            <span class="status-icon">🏁</span>
                            <span class="status-text"><?php _e('SUBASTA FINALIZADA', 'vicino-auction'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shortcode 3: vicino_auction_list -->
        <!-- <div class="vicino-auction-shortcode-card">
            <div class="vicino-auction-shortcode-header">
                <h3>
                    <span class="dashicons dashicons-list-view"></span>
                    [vicino_auction_list]
                </h3>
                <span class="vicino-auction-shortcode-badge vicino-auction-badge-list"><?php _e('Lista', 'vicino-auction'); ?></span>
            </div>
            
            <div class="vicino-auction-shortcode-content">
                <p class="vicino-auction-shortcode-description">
                    <?php _e('Muestra una lista de todos los lotes de subasta activos con información básica como título, precio inicial, preoferta actual y tiempo restante.', 'vicino-auction'); ?>
                </p>
                
                <div class="vicino-auction-shortcode-params">
                    <h4><?php _e('Parámetros:', 'vicino-auction'); ?></h4>
                    <ul>
                        <li>
                            <code>limit</code> - <?php _e('Número máximo de lotes a mostrar (por defecto: 10)', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <code>category</code> - <?php _e('Filtrar por categoría específica (opcional)', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <code>orderby</code> - <?php _e('Ordenar por: date, title, price (por defecto: date)', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <code>order</code> - <?php _e('Orden: ASC o DESC (por defecto: DESC)', 'vicino-auction'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="vicino-auction-shortcode-examples">
                    <h4><?php _e('Ejemplos de uso:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_list]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_list]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_list limit="5" orderby="title" order="ASC"]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_list limit=&quot;5&quot; orderby=&quot;title&quot; order=&quot;ASC&quot;]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div> -->

        <!-- Shortcode 4: vicino_auction_results_table -->
        <div class="vicino-auction-shortcode-card">
            <div class="vicino-auction-shortcode-header">
                <h3>
                    <span class="dashicons dashicons-editor-table"></span>
                    [vicino_auction_results_table]
                </h3>
                <span class="vicino-auction-shortcode-badge vicino-auction-badge-table"><?php _e('Tabla', 'vicino-auction'); ?></span>
            </div>
            
            <div class="vicino-auction-shortcode-content">
                <p class="vicino-auction-shortcode-description">
                    <?php _e('Muestra una tabla completa con los resultados de la subasta, incluyendo información de todos los participantes, sus preofertas y el estado de cada lote. La tabla está ordenada alfabéticamente por título de lote (A-Z).', 'vicino-auction'); ?>
                </p>
                
                <div class="vicino-auction-shortcode-params">
                    <h4><?php _e('Parámetros:', 'vicino-auction'); ?></h4>
                    <ul>
                        <li>
                            <code>show_filter</code> - <?php _e('Mostrar filtros por lote: true/false (por defecto: true)', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <code>show_export</code> - <?php _e('Mostrar botón de exportación: true/false (por defecto: false)', 'vicino-auction'); ?>
                        </li>
                        <li>
                            <code>limit</code> - <?php _e('Límite de registros a mostrar: número o -1 para todos (por defecto: -1)', 'vicino-auction'); ?>
                        </li>
                    </ul>
                </div>
                
                <div class="vicino-auction-shortcode-examples">
                    <h4><?php _e('Ejemplos de uso:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_results_table]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_results_table]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_auction_results_table show_filter="false" show_export="true"]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_auction_results_table show_filter=&quot;false&quot; show_export=&quot;true&quot;]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shortcode 5: vicino_export_results -->
        <div class="vicino-auction-shortcode-card">
            <div class="vicino-auction-shortcode-header">
                <h3>
                    <span class="dashicons dashicons-download"></span>
                    [vicino_export_results]
                </h3>
                <span class="vicino-auction-shortcode-badge vicino-auction-badge-export"><?php _e('Exportar', 'vicino-auction'); ?></span>
            </div>
            
            <div class="vicino-auction-shortcode-content">
                <p class="vicino-auction-shortcode-description">
                    <?php _e('Muestra un botón que permite a los usuarios autenticados exportar los resultados de la subasta en formato Excel. Solo visible para usuarios con sesión iniciada.', 'vicino-auction'); ?>
                </p>
                
                <div class="vicino-auction-shortcode-params">
                    <h4><?php _e('Parámetros:', 'vicino-auction'); ?></h4>
                    <p class="vicino-auction-no-params"><?php _e('Este shortcode no acepta parámetros adicionales.', 'vicino-auction'); ?></p>
                </div>
                
                <div class="vicino-auction-shortcode-examples">
                    <h4><?php _e('Ejemplo de uso:', 'vicino-auction'); ?></h4>
                    <div class="vicino-auction-code-block">
                        <code>[vicino_export_results]</code>
                        <button class="vicino-auction-copy-btn" data-clipboard-text="[vicino_export_results]">
                            <span class="dashicons dashicons-admin-page"></span>
                        </button>
                    </div>
                </div>
                
                <div class="vicino-auction-shortcode-note">
                    <p>
                        <span class="dashicons dashicons-info"></span>
                        <strong><?php _e('Nota:', 'vicino-auction'); ?></strong>
                        <?php _e('Este shortcode requiere que el usuario esté autenticado. Los usuarios no registrados verán un mensaje solicitando iniciar sesión.', 'vicino-auction'); ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tips and Best Practices -->
    <div class="vicino-auction-tips-section">
        <div class="vicino-auction-card">
            <div class="vicino-auction-card-header">
                <h2>
                    <span class="dashicons dashicons-lightbulb"></span>
                    <?php _e('Consejos y Mejores Prácticas', 'vicino-auction'); ?>
                </h2>
            </div>
            
            <div class="vicino-auction-tips-content">
                <div class="vicino-auction-tips-grid">
                    <div class="vicino-auction-tip">
                        <h4>
                            <span class="dashicons dashicons-yes"></span>
                            <?php _e('Combinación de Shortcodes', 'vicino-auction'); ?>
                        </h4>
                        <p><?php _e('Puede combinar múltiples shortcodes en la misma página. Por ejemplo, use [vicino_auction_list] para mostrar todos los lotes y [vicino_auction_status] en cada elemento de la lista.', 'vicino-auction'); ?></p>
                    </div>
                    
                    <div class="vicino-auction-tip">
                        <h4>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Rendimiento', 'vicino-auction'); ?>
                        </h4>
                        <p><?php _e('Para páginas con muchos lotes, considere usar el parámetro "limit" en [vicino_auction_list] para mejorar el tiempo de carga.', 'vicino-auction'); ?></p>
                    </div>
                    
                    <div class="vicino-auction-tip">
                        <h4>
                            <span class="dashicons dashicons-smartphone"></span>
                            <?php _e('Responsive Design', 'vicino-auction'); ?>
                        </h4>
                        <p><?php _e('Todos los shortcodes están optimizados para dispositivos móviles y se adaptan automáticamente al tamaño de pantalla.', 'vicino-auction'); ?></p>
                    </div>
                    
                    <div class="vicino-auction-tip">
                        <h4>
                            <span class="dashicons dashicons-update"></span>
                            <?php _e('Actualizaciones en Tiempo Real', 'vicino-auction'); ?>
                        </h4>
                        <p><?php _e('Los shortcodes [vicino_auction] incluyen funcionalidad AJAX para actualizaciones automáticas sin recargar la página.', 'vicino-auction'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
