<?php

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Activator {

    /**
     * Create necessary database tables and set default options during plugin activation.
     *
     * @since    1.0.0
     */
    public static function activate() {
        global $wpdb;
        
        // Create bids table
        $table_name = $wpdb->prefix . 'vicino_auction_bids';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            lot_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            bid_amount decimal(15,2) NOT NULL,
            bid_date datetime NOT NULL,
            bid_status varchar(20) NOT NULL DEFAULT 'active',
            PRIMARY KEY  (id),
            KEY lot_id (lot_id),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Set default options if they don't exist
        if (!get_option('vicino_auction_start_date')) {
            // Default start date: current date
            update_option('vicino_auction_start_date', date('Y-m-d H:i:s'));
        }
        
        if (!get_option('vicino_auction_end_date')) {
            // Default end date: 7 days from now
            update_option('vicino_auction_end_date', date('Y-m-d H:i:s', strtotime('+7 days')));
        }
        
        if (!get_option('vicino_auction_sniping_minutes')) {
            // Default sniping extension: 5 minutes
            update_option('vicino_auction_sniping_minutes', 5);
        }
        
        if (!get_option('vicino_auction_sniping_extension')) {
            // Default extension time: 10 minutes
            update_option('vicino_auction_sniping_extension', 10);
        }
        
        // Create custom post status for "Ofertado" (Pre-bid)
        register_post_status('prebid', array(
            'label' => _x('Ofertado', 'post status', 'vicino-auction'),
            'public' => true,
            'exclude_from_search' => false,
            'show_in_admin_all_list' => true,
            'show_in_admin_status_list' => true,
            'label_count' => _n_noop('Preofertado <span class="count">(%s)</span>', 'Ofertados <span class="count">(%s)</span>', 'vicino-auction')
        ));
        
        // Flush rewrite rules to ensure our custom post status works
        flush_rewrite_rules();
    }
}