<?php

/**
 * Handles cleanup tasks for the Vicino Auction plugin.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Cleanup {

    /**
     * Initialize the class and set up hooks.
     *
     * @since    1.0.0
     */
    public function __construct() {
        add_action('vicino_auction_cleanup_temp_file', array($this, 'cleanup_temp_file'));
    }

    /**
     * Clean up temporary export files.
     *
     * @since    1.0.0
     * @param    string    $file_path    The path to the temporary file.
     */
    public function cleanup_temp_file($file_path) {
        if (file_exists($file_path)) {
            @unlink($file_path);
        }
    }

    /**
     * Clean up all temporary files in the export directory.
     *
     * @since    1.0.0
     */
    public function cleanup_all_temp_files() {
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/vicino-auction-temp';
        
        if (file_exists($temp_dir) && is_dir($temp_dir)) {
            $files = glob($temp_dir . '/*');
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    @unlink($file);
                }
            }
        }
    }
}