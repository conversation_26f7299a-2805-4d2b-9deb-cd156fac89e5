<?php

/**
 * Fired during plugin deactivation.
 *
 * This class defines all code necessary to run during the plugin's deactivation.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Deactivator {

    /**
     * Clean up scheduled events and perform other deactivation tasks.
     *
     * @since    1.0.0
     */
    public static function deactivate() {
        // Clear the scheduled auction check event
        $timestamp = wp_next_scheduled('vicino_auction_check_end_times');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'vicino_auction_check_end_times');
        }
        
        // Remove the scheduled hook completely
        wp_clear_scheduled_hook('vicino_auction_check_end_times');
        
        // Note: We don't delete the database tables or options here
        // to preserve user data in case the plugin is reactivated.
        // For a complete uninstall, a separate uninstall.php file would be used.
    }
}