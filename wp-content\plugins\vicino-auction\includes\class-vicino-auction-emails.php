<?php

/**
 * Class for handling email notifications.
 *
 * This class manages email notifications for auction events such as
 * outbids, auction closings, and other important notifications.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Emails {

    /**
     * The lot manager instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Vicino_Auction_Lot    $lot_manager    The lot manager instance.
     */
    private $lot_manager;

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->lot_manager = new Vicino_Auction_Lot();
    }

    /**
     * Send an outbid notification to a user.
     *
     * @since    1.0.0
     * @param    int     $user_id      The ID of the user to notify.
     * @param    int     $lot_id       The ID of the lot.
     * @param    float   $new_bid      The new bid amount that outbid the user.
     * @return   bool                  True if the email was sent, false otherwise.
     */
    public function send_outbid_notification($user_id, $lot_id, $new_bid) {
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }

        $lot = get_post($lot_id);
        if (!$lot) {
            return false;
        }

        $subject = sprintf(__('Te han superado la puja en %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('Hola %s,', 'vicino-auction') . "\n\n" .
            __('Alguien ha realizado una puja superior de %s en "%s".', 'vicino-auction') . "\n\n" .
            __('Si deseas seguir pujando, por favor visita la página de la subasta:', 'vicino-auction') . "\n" .
            '%s' . "\n\n" .
            __('¡Gracias por participar en nuestra subasta!', 'vicino-auction') . "\n\n" .
            get_bloginfo('name'),
            $user->display_name,
            $this->lot_manager->format_price($new_bid),
            $lot->post_title,
            get_permalink($lot_id)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($user->user_email, $subject, $message, $headers);
    }

    /**
     * Send a notification to the admin about a new bid.
     *
     * @since    1.0.0
     * @param    int     $lot_id       The ID of the lot.
     * @param    int     $user_id      The ID of the user who placed the bid.
     * @param    float   $bid_amount   The bid amount.
     * @return   bool                  True if the email was sent, false otherwise.
     */
    public function send_admin_bid_notification($lot_id, $user_id, $bid_amount) {
        $admin_email = get_option('admin_email');
        $user = get_userdata($user_id);
        $lot = get_post($lot_id);

        if (!$user || !$lot) {
            return false;
        }

        $subject = sprintf(__('Nueva Preoferta en %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('Se ha realizado una nueva Preoferta:', 'vicino-auction') . "\n\n" .
            __('Lote: %s (ID: %d)', 'vicino-auction') . "\n" .
            __('Postor: %s (ID: %d)', 'vicino-auction') . "\n" .
            __('Monto de la Preoferta: %s', 'vicino-auction') . "\n" .
            __('Fecha: %s', 'vicino-auction') . "\n\n" .
            __('Ver lote: %s', 'vicino-auction'),
            $lot->post_title,
            $lot_id,
            $user->user_login,
            $user_id,
            $this->lot_manager->format_price($bid_amount),
            current_time('mysql'),
            get_permalink($lot_id)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($admin_email, $subject, $message, $headers);
    }

    /**
     * Send a successful bid notification to a user.
     *
     * @since    1.0.0
     * @param    int     $user_id      The ID of the user who placed the bid.
     * @param    int     $lot_id       The ID of the lot.
     * @param    float   $bid_amount   The bid amount.
     * @return   bool                  True if the email was sent, false otherwise.
     */
    public function send_successful_bid_notification($user_id, $lot_id, $bid_amount) {
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }

        $lot = get_post($lot_id);
        if (!$lot) {
            return false;
        }

        $subject = sprintf(__('Puja realizada con éxito en %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('Hola %s,', 'vicino-auction') . "\n\n" .
            __('Tu puja de %s en "%s" se ha realizado correctamente.', 'vicino-auction') . "\n\n" .
            __('Puedes ver la subasta aquí:', 'vicino-auction') . "\n" .
            '%s' . "\n\n" .
            __('¡Gracias por participar en nuestra subasta!', 'vicino-auction') . "\n\n" .
            get_bloginfo('name'),
            $user->display_name,
            $this->lot_manager->format_price($bid_amount),
            $lot->post_title,
            get_permalink($lot_id)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($user->user_email, $subject, $message, $headers);
    }

    /**
     * Send an auction win notification to a user.
     *
     * @since    1.0.0
     * @param    int     $user_id      The ID of the winning user.
     * @param    int     $lot_id       The ID of the lot.
     * @param    float   $winning_bid  The winning bid amount.
     * @return   bool                  True if the email was sent, false otherwise.
     */
    // Activated as this is the function being called
    public function send_auction_win_notification($user_id, $lot_id, $winning_bid) {
        $user = get_userdata($user_id);
        if (!$user) {
            return false;
        }

        $lot = get_post($lot_id);
        if (!$lot) {
            return false;
        }

        $subject = sprintf(__('¡Enhorabuena! Has ganado la subasta de %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('Hola %s,', 'vicino-auction') . "\n\n" .
            __('¡Enhorabuena! Has ganado la subasta de \"%s\" con una puja de %s.', 'vicino-auction') . "\n\n" .
            __('Puedes ver los detalles de la subasta aquí:', 'vicino-auction') . "\n" .
            '%s' . "\n\n" .
            __('¡Gracias por participar en nuestra subasta!', 'vicino-auction') . "\n\n" .
            get_bloginfo('name'),
            $user->display_name,
            $lot->post_title,
            $this->lot_manager->format_price($winning_bid),
            get_permalink($lot_id)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($user->user_email, $subject, $message, $headers);
    }

    /**
     * Send a notification to the winner of an auction.
     *
     * @since    1.0.0
     * @param    int     $lot_id       The ID of the lot.
     * @param    int     $user_id      The ID of the winning user.
     * @param    float   $bid_amount   The winning bid amount.
     * @return   bool                  True if the email was sent, false otherwise.
     */
    /* // Commented out as it's not used - send_auction_win_notification is used instead
    public function send_winner_notification($lot_id, $user_id, $bid_amount) {
        $user = get_userdata($user_id);
        $lot = get_post($lot_id);

        if (!$user || !$lot) {
            return false;
        }

        $subject = sprintf(__('¡Enhorabuena! Has ganado la subasta de %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('Hola %s,', 'vicino-auction') . "\n\n" .
            __('¡Enhorabuena! Eres el ganador de la subasta de "%s" con una puja de %s.', 'vicino-auction') . "\n\n" .
            __('Detalles del Lote:', 'vicino-auction') . "\n" .
            __('Título: %s', 'vicino-auction') . "\n" .
            __('Tu Puja Ganadora: %s', 'vicino-auction') . "\n\n" .
            __('Por favor, contáctanos para organizar el pago y la entrega.', 'vicino-auction') . "\n\n" .
            __('¡Gracias por participar en nuestra subasta!', 'vicino-auction') . "\n\n" .
            get_bloginfo('name'),
            $user->display_name,
            $lot->post_title, // First %s in message
            $this->lot_manager->format_price($bid_amount), // Second %s in message
            $lot->post_title, // Third %s (Title detail)
            $this->lot_manager->format_price($bid_amount) // Fourth %s (Winning Bid detail)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($user->user_email, $subject, $message, $headers);
    }
    */

    /**
     * Send a notification to the admin about an auction ending.
     *
     * @since    1.0.0
     * @param    int     $lot_id       The ID of the lot.
     * @param    int     $winner_id    The ID of the winning user (or null if no winner).
     * @param    float   $final_bid    The final bid amount (or null if no bids).
     * @return   bool                  True if the email was sent, false otherwise.
     */
    /* // Commented out as per request
    public function send_admin_auction_end_notification($lot_id, $winner_id = null, $final_bid = null) {
        $admin_email = get_option('admin_email');
        $lot = get_post($lot_id);

        if (!$lot) {
            return false;
        }

        $subject = sprintf(__('Auction ended: %s', 'vicino-auction'), $lot->post_title);

        if ($winner_id && $final_bid) {
            $winner = get_userdata($winner_id);
            $message = sprintf(
                __('The auction for "%s" has ended.', 'vicino-auction') . "\n\n" .
                __('Winner: %s (ID: %d)', 'vicino-auction') . "\n" .
                __('Final Bid: %s', 'vicino-auction') . "\n\n" .
                __('View lot: %s', 'vicino-auction'),
                $lot->post_title,
                $winner ? $winner->user_login : __('Unknown User', 'vicino-auction'),
                $winner_id,
                $this->lot_manager->format_price($final_bid),
                get_permalink($lot_id)
            );
        } else {
            $message = sprintf(
                __('The auction for "%s" has ended with no bids.', 'vicino-auction') . "\n\n" .
                __('View lot: %s', 'vicino-auction'),
                $lot->post_title,
                get_permalink($lot_id)
            );
        }

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($admin_email, $subject, $message, $headers);
    }
    */

    /**
     * Send a notification about auction time extension due to sniping.
     *
     * @since    1.0.0
     * @param    int       $lot_id         The ID of the lot.
     * @param    string    $new_end_date   The new end date for the auction.
     * @return   bool                      True if the email was sent, false otherwise.
     */
    /* // Commented out as per request
    public function send_time_extension_notification($lot_id, $new_end_date) {
        $admin_email = get_option('admin_email');
        $lot = get_post($lot_id);

        if (!$lot) {
            return false;
        }

        $subject = sprintf(__('Auction time extended: %s', 'vicino-auction'), $lot->post_title);

        $message = sprintf(
            __('The auction time for "%s" has been extended due to last-minute bidding.', 'vicino-auction') . "\n\n" .
            __('New end time: %s', 'vicino-auction') . "\n\n" .
            __('View lot: %s', 'vicino-auction'),
            $lot->post_title,
            $new_end_date,
            get_permalink($lot_id)
        );

        $headers = array('Content-Type: text/plain; charset=UTF-8');

        return wp_mail($admin_email, $subject, $message, $headers);
    }
    */
}