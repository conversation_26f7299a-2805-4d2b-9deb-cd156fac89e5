<?php

/**
 * Class for handling Excel exports.
 *
 * This class manages the generation of Excel files for auction data,
 * allowing administrators to export bid information and auction results.
 *
 * @since      1.0.0
 */
class Vicino_Auction_Excel {

    /**
     * The lot manager instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Vicino_Auction_Lot    $lot_manager    The lot manager instance.
     */
    private $lot_manager;

    /**
     * The bid manager instance.
     *
     * @since    1.0.0
     * @access   private
     * @var      Vicino_Auction_Bid    $bid_manager    The bid manager instance.
     */
    private $bid_manager;

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->lot_manager = new Vicino_Auction_Lot();
        $this->bid_manager = new Vicino_Auction_Bid();
    }

    /**
     * Generate an Excel file with auction data.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs to include in the export.
     * @return   string               Path to the generated Excel file.
     */
    public function generate_excel($lot_ids = array()) {
        // If no specific lots are provided, get all active lots
        if (empty($lot_ids)) {
            $lots = $this->lot_manager->get_active_lots();
            $lot_ids = wp_list_pluck($lots, 'ID');
        }

        // Create a temporary file
        $temp_file = tempnam(sys_get_temp_dir(), 'vicino_auction_');
        $file = fopen($temp_file, 'w');

        // Add UTF-8 BOM for Excel to recognize special characters
        fputs($file, "\xEF\xBB\xBF");

        // Add headers (removed 'Lot ID' and 'Bid Status')
        $headers = array(
            __('Lot Title', 'vicino-auction'),
            __('Bidder Username', 'vicino-auction'),
            __('Full Name', 'vicino-auction'),
            __('Bidder Email', 'vicino-auction'),
            __('Bidder Phone', 'vicino-auction'),
            __('Bid Amount', 'vicino-auction'),
            __('Bid Date', 'vicino-auction')
        );
        fputcsv($file, $headers);

        // Collect data for sorting
        $data_rows = array();

        foreach ($lot_ids as $lot_id) {
            $bids = $this->bid_manager->get_lot_bids($lot_id);
            $lot_title = get_the_title($lot_id);

            if (!empty($bids)) {
                foreach ($bids as $bid) {
                    $user = get_userdata($bid->user_id);
                    $phone = get_user_meta($bid->user_id, 'phone', true);

                    // Get full name
                    $first_name = get_user_meta($bid->user_id, 'first_name', true);
                    $last_name = get_user_meta($bid->user_id, 'last_name', true);
                    $full_name = trim($first_name . ' ' . $last_name);
                    if (empty($full_name)) {
                        $full_name = $user ? $user->display_name : '';
                    }

                    $data_rows[] = array(
                        'lot_title' => $lot_title,
                        'username' => $user ? $user->user_login : __('Unknown User', 'vicino-auction'),
                        'full_name' => $full_name,
                        'email' => $user ? $user->user_email : '',
                        'phone' => $phone ? $phone : '',
                        'bid_amount' => $this->lot_manager->format_price($bid->bid_amount),
                        'bid_date' => $bid->bid_date
                    );
                }
            } else {
                // Include lots with no bids
                $data_rows[] = array(
                    'lot_title' => $lot_title,
                    'username' => __('No bids', 'vicino-auction'),
                    'full_name' => '',
                    'email' => '',
                    'phone' => '',
                    'bid_amount' => $this->lot_manager->format_price($this->lot_manager->get_starting_price($lot_id)),
                    'bid_date' => ''
                );
            }
        }

        // Sort data rows alphabetically by 'lot_title' (A to Z)
        usort($data_rows, function($a, $b) {
            return strcasecmp($a['lot_title'], $b['lot_title']);
        });

        // Write sorted data to CSV
        foreach ($data_rows as $row) {
            $csv_row = array(
                $row['lot_title'],
                $row['username'],
                $row['full_name'],
                $row['email'],
                $row['phone'],
                $row['bid_amount'],
                $row['bid_date']
            );
            fputcsv($file, $csv_row);
        }

        fclose($file);
        return $temp_file;
    }

    /**
     * Export auction data as an Excel file and trigger download.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs to include in the export.
     */
    public function export_to_excel($lot_ids = array()) {
        $file_path = $this->generate_excel($lot_ids);
        $filename = 'auction_data_' . date('Y-m-d_H-i-s') . '.csv';

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($file_path));
        header('Pragma: no-cache');
        header('Expires: 0');

        // Output file
        readfile($file_path);

        // Delete temporary file
        @unlink($file_path);

        exit;
    }

    /**
     * Generate an Excel file with auction winners data.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs to include in the export.
     * @return   string               Path to the generated Excel file.
     */
    public function generate_winners_excel($lot_ids = array()) {
        // If no specific lots are provided, get all active lots
        if (empty($lot_ids)) {
            $lots = $this->lot_manager->get_active_lots();
            $lot_ids = wp_list_pluck($lots, 'ID');
        }

        // Create a temporary file
        $temp_file = tempnam(sys_get_temp_dir(), 'vicino_auction_winners_');
        $file = fopen($temp_file, 'w');

        // Add UTF-8 BOM for Excel to recognize special characters
        fputs($file, "\xEF\xBB\xBF");

        // Add headers (removed 'Lot ID')
        $headers = array(
            __('Lot Title', 'vicino-auction'),
            __('Winner Username', 'vicino-auction'),
            __('Winner Full Name', 'vicino-auction'),
            __('Winner Email', 'vicino-auction'),
            __('Winner Phone', 'vicino-auction'),
            __('Winning Bid', 'vicino-auction'),
            __('Bid Date', 'vicino-auction')
        );
        fputcsv($file, $headers);

        // Collect data for sorting
        $data_rows = array();

        foreach ($lot_ids as $lot_id) {
            $winner_id = $this->lot_manager->get_current_bidder($lot_id);
            $lot_title = get_the_title($lot_id);

            if ($winner_id) {
                $user = get_userdata($winner_id);
                $phone = get_user_meta($winner_id, 'phone', true);
                $winning_bid = $this->lot_manager->get_current_bid($lot_id);

                // Get full name
                $first_name = get_user_meta($winner_id, 'first_name', true);
                $last_name = get_user_meta($winner_id, 'last_name', true);
                $full_name = trim($first_name . ' ' . $last_name);
                if (empty($full_name)) {
                    $full_name = $user ? $user->display_name : '';
                }

                // Get the winning bid's date
                global $wpdb;
                $table_name = $wpdb->prefix . 'vicino_auction_bids';
                $bid_date = $wpdb->get_var($wpdb->prepare(
                    "SELECT bid_date FROM $table_name
                    WHERE lot_id = %d AND user_id = %d AND bid_amount = %f AND bid_status = 'active'
                    LIMIT 1",
                    $lot_id, $winner_id, $winning_bid
                ));

                $data_rows[] = array(
                    'lot_title' => $lot_title,
                    'username' => $user ? $user->user_login : __('Unknown User', 'vicino-auction'),
                    'full_name' => $full_name,
                    'email' => $user ? $user->user_email : '',
                    'phone' => $phone ? $phone : '',
                    'winning_bid' => $this->lot_manager->format_price($winning_bid),
                    'bid_date' => $bid_date
                );
            } else {
                // Include lots with no winner
                $data_rows[] = array(
                    'lot_title' => $lot_title,
                    'username' => __('No winner', 'vicino-auction'),
                    'full_name' => '',
                    'email' => '',
                    'phone' => '',
                    'winning_bid' => $this->lot_manager->format_price($this->lot_manager->get_starting_price($lot_id)),
                    'bid_date' => ''
                );
            }
        }

        // Sort data rows alphabetically by 'lot_title' (A to Z)
        usort($data_rows, function($a, $b) {
            return strcasecmp($a['lot_title'], $b['lot_title']);
        });

        // Write sorted data to CSV
        foreach ($data_rows as $row) {
            $csv_row = array(
                $row['lot_title'],
                $row['username'],
                $row['full_name'],
                $row['email'],
                $row['phone'],
                $row['winning_bid'],
                $row['bid_date']
            );
            fputcsv($file, $csv_row);
        }

        fclose($file);
        return $temp_file;
    }

    /**
     * Export auction winners data as an Excel file and trigger download.
     *
     * @since    1.0.0
     * @param    array    $lot_ids    Array of lot IDs to include in the export.
     */
    public function export_winners_to_excel($lot_ids = array()) {
        $file_path = $this->generate_winners_excel($lot_ids);
        $filename = 'auction_winners_' . date('Y-m-d_H-i-s') . '.csv';

        // Set headers for download
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($file_path));
        header('Pragma: no-cache');
        header('Expires: 0');

        // Output file
        readfile($file_path);

        // Delete temporary file
        @unlink($file_path);

        exit;
    }
}