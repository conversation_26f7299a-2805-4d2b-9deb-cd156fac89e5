<?php

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.0.0
 */
class Vicino_Auction {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      Vicino_Auction_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * Set the plugin name and the plugin version that can be used throughout the plugin.
     * Load the dependencies, define the locale, and set the hooks for the admin area and
     * the public-facing side of the site.
     *
     * @since    1.0.0
     */
    public function __construct() {
        if (defined('VICINO_AUCTION_VERSION')) {
            $this->version = VICINO_AUCTION_VERSION;
        } else {
            $this->version = '1.0.0';
        }
        $this->plugin_name = 'vicino-auction';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();
        $this->define_shortcodes();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * Include the following files that make up the plugin:
     *
     * - Vicino_Auction_Loader. Orchestrates the hooks of the plugin.
     * - Vicino_Auction_i18n. Defines internationalization functionality.
     * - Vicino_Auction_Admin. Defines all hooks for the admin area.
     * - Vicino_Auction_Public. Defines all hooks for the public side of the site.
     * - Vicino_Auction_Shortcodes. Defines all shortcodes for the plugin.
     *
     * Create an instance of the loader which will be used to register the hooks
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {

        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-i18n.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/admin/class-vicino-auction-admin.php';

        /**
         * The class responsible for cleaning up temporary files.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-cleanup.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/public/class-vicino-auction-public.php';

        /**
         * The class responsible for defining all shortcodes.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-shortcodes.php';

        /**
         * The class responsible for handling auction lots.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-lot.php';

        /**
         * The class responsible for handling bids.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-bid.php';

        /**
         * The class responsible for handling Excel exports.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-excel.php';

        /**
         * The class responsible for handling email notifications.
         */
        require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-emails.php';

        $this->loader = new Vicino_Auction_Loader();

    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * Uses the Vicino_Auction_i18n class in order to set the domain and to register the hook
     * with WordPress.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale() {

        $plugin_i18n = new Vicino_Auction_i18n();

        $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');

    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks() {

        $plugin_admin = new Vicino_Auction_Admin($this->get_plugin_name(), $this->get_version());

        // Admin scripts and styles
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');

        // Admin menu items
        $this->loader->add_action('admin_menu', $plugin_admin, 'add_admin_menu');

        // Register settings
        $this->loader->add_action('admin_init', $plugin_admin, 'register_settings');

        // Add meta boxes for auction lots
        $this->loader->add_action('add_meta_boxes', $plugin_admin, 'add_meta_boxes');
        $this->loader->add_action('save_post', $plugin_admin, 'save_meta_boxes', 10, 2);

        // Ajax handlers for admin
        $this->loader->add_action('wp_ajax_vicino_auction_export_excel', $plugin_admin, 'export_excel');
        $this->loader->add_action('wp_ajax_vicino_auction_bulk_activate', $plugin_admin, 'bulk_activate_lots');
        $this->loader->add_action('wp_ajax_vicino_auction_bulk_deactivate', $plugin_admin, 'bulk_deactivate_lots');
        $this->loader->add_action('wp_ajax_vicino_auction_delete_bid', $plugin_admin, 'delete_bid');
        $this->loader->add_action('wp_ajax_vicino_auction_reset', $plugin_admin, 'reset_auction');
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks() {

        $plugin_public = new Vicino_Auction_Public($this->get_plugin_name(), $this->get_version());

        // Public scripts and styles
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');

        // Ajax handlers for public
        $this->loader->add_action('wp_ajax_vicino_auction_place_bid', $plugin_public, 'place_bid');
        $this->loader->add_action('wp_ajax_nopriv_vicino_auction_place_bid', $plugin_public, 'handle_not_logged_in');
        $this->loader->add_action('wp_ajax_vicino_frontend_export', $plugin_public, 'handle_frontend_export'); // Add frontend export AJAX handler
        $this->loader->add_action('wp_ajax_nopriv_vicino_frontend_export', $plugin_public, 'handle_not_logged_in'); // Prevent non-logged-in users

        // Cron job for checking auction end times
        $this->loader->add_action('vicino_auction_check_end_times', $plugin_public, 'check_end_times');
        if (!wp_next_scheduled('vicino_auction_check_end_times')) {
            wp_schedule_event(time(), 'hourly', 'vicino_auction_check_end_times');
        }
    }

    /**
     * Register all of the shortcodes related to the functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_shortcodes() {

        $plugin_shortcodes = new Vicino_Auction_Shortcodes($this->get_plugin_name(), $this->get_version());

        add_shortcode('vicino_auction', array($plugin_shortcodes, 'auction_shortcode'));
        add_shortcode('vicino_auction_list', array($plugin_shortcodes, 'auction_list_shortcode'));
        add_shortcode('vicino_auction_status', array($plugin_shortcodes, 'auction_status_shortcode'));
        add_shortcode('vicino_export_results', array($plugin_shortcodes, 'vicino_export_results_shortcode')); // Add export results shortcode
        add_shortcode('vicino_auction_results_table', array($plugin_shortcodes, 'auction_results_table_shortcode')); // Add results table shortcode

    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run() {
        // Schedule the event to check auction end times
        if (!wp_next_scheduled('vicino_auction_check_end_times')) {
            wp_schedule_event(time(), 'minute', 'vicino_auction_check_end_times');
        }

        // Initialize the cleanup class
        new Vicino_Auction_Cleanup();

        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.0.0
     * @return    Vicino_Auction_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }

}