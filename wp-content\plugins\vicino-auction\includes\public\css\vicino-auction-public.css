/**
 * All of the CSS for your public-facing functionality should be
 * included in this file.
 */

/* Main container styles */
.vicino-auction-container {
    margin-bottom: 40px;
    padding: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    font-size: 16px;
    max-width: 100%;
    overflow: hidden;
}

/* Title styles */
.vicino-auction-title {
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 28px;
    font-weight: 700;
    color: #333333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

/* Status indicator styles */
.vicino-auction-status {
    display: inline-block;
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 18px;
}

.vicino-auction-active {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 5px solid #2e7d32;
}

.vicino-auction-inactive {
    background-color: #f5f5f5;
    color: #757575;
    border-left: 5px solid #757575;
}

.status-icon {
    margin-right: 8px;
    font-size: 20px;
    vertical-align: middle;
}

/* Price information styles */
.vicino-auction-price-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eeeeee;
}

.vicino-auction-starting-price,
.vicino-auction-current-bid {
    margin-bottom: 15px;
    font-size: 18px;
}

.vicino-auction-price-info .label {
    font-weight: bold;
    margin-right: 10px;
    color: #555555;
}

.vicino-auction-price-info .price {
    font-weight: bold;
    font-size: 20px;
    color: #1976d2;
}

/* Status badge styles */
.vicino-auction-status-badge {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 6px;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 15px;
}

.vicino-status-not-started {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left: 5px solid #1976d2;
}

.vicino-status-active {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 5px solid #2e7d32;
}

.vicino-status-has-bids {
    background-color: #fff3e0;
    color: #e65100;
    border-left: 5px solid #e65100;
}

.vicino-status-finished {
    background-color: #f5f5f5;
    color: #616161;
    border-left: 5px solid #616161;
}

.vicino-auction-status-badge .status-icon {
    margin-right: 8px;
    font-size: 18px;
    vertical-align: middle;
}

.vicino-auction-status-badge .status-text {
    vertical-align: middle;
}

/* Bid history styles */
.vicino-auction-bid-history {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eeeeee;
}

.vicino-auction-bid-history h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 22px;
    color: #333333;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.vicino-bid-history-table-container {
    overflow-x: auto;
    margin-bottom: 15px;
}

.vicino-bid-history-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 16px;
}

.vicino-bid-history-table th {
    background-color: #f0f0f0;
    color: #333333;
    font-weight: bold;
    text-align: left;
    padding: 12px 15px;
    border-bottom: 2px solid #dddddd;
}

.vicino-bid-history-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eeeeee;
    vertical-align: middle;
}

.vicino-bid-history-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.vicino-bid-history-table tr:hover {
    background-color: #f0f7ff;
}

.vicino-bid-history-table .bid-amount {
    font-weight: bold;
    color: #1976d2;
}

.no-bids-message {
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 5px;
    color: #757575;
    font-style: italic;
    text-align: center;
}

/* Bid form styles */
.vicino-auction-bid-form {
    margin-top: 30px;
    padding: 25px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eeeeee;
}

.vicino-auction-bid-form h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 22px;
    color: #333333;
}

.vicino-auction-bid-form .form-group {
    margin-bottom: 20px;
}

.vicino-auction-bid-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: 18px;
    color: #555555;
}

.vicino-auction-bid-form input[type="number"] {
    width: 100%;
    max-width: 300px;
    padding: 12px 15px;
    font-size: 18px;
    border: 2px solid #dddddd;
    border-radius: 6px;
    transition: border-color 0.3s;
}

.vicino-auction-bid-form input[type="number"]:focus {
    border-color: #1976d2;
    outline: none;
}

.vicino-place-bid-button {
    background-color: #1976d2;
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.3s;
}

.vicino-place-bid-button:hover {
    background-color: #1565c0;
}

.vicino-bid-message {
    margin-top: 20px;
    padding: 15px;
    border-radius: 5px;
    font-size: 16px;
}

.vicino-bid-message .success {
    color: #2e7d32;
    background-color: #e8f5e9;
    padding: 10px 15px;
    border-radius: 5px;
    display: block;
}

.vicino-bid-message .error {
    color: #d32f2f;
    background-color: #ffebee;
    padding: 10px 15px;
    border-radius: 5px;
    display: block;
}

.vicino-bid-message .loading {
    color: #666666;
    font-style: italic;
    display: block;
}

/* Winner display styles */
.vicino-auction-winner {
    margin-top: 30px;
    padding: 25px;
    background-color: #fff8e1;
    border-radius: 8px;
    border: 1px solid #ffe082;
}

.vicino-auction-winner h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 22px;
    color: #ff8f00;
}

.winner-info {
    font-size: 18px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.winner-icon {
    font-size: 24px;
    margin-right: 10px;
}

.winner-name {
    font-weight: bold;
    color: #333333;
    margin-right: 5px;
}

.winning-bid {
    font-weight: bold;
    color: #1976d2;
}

.vicino-auction-no-winner {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 8px;
    text-align: center;
    color: #757575;
}

.vicino-login-message {
    padding: 15px;
    background-color: #e3f2fd;
    border-radius: 5px;
    font-size: 18px;
    text-align: center;
}

.vicino-login-message a {
    color: #1976d2;
    font-weight: bold;
    text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .vicino-auction-container {
        padding: 20px;
    }
    
    .vicino-auction-title {
        font-size: 24px;
    }
    
    .vicino-auction-status {
        font-size: 16px;
    }
    
    .vicino-auction-price-info,
    .vicino-auction-bid-history,
    .vicino-auction-bid-form,
    .vicino-auction-winner {
        padding: 15px;
    }
    
    .vicino-bid-history-table th,
    .vicino-bid-history-table td {
        padding: 10px;
        font-size: 14px;
    }
    
    .vicino-auction-bid-form label,
    .vicino-auction-bid-form input[type="number"] {
        font-size: 16px;
    }
    
    .vicino-place-bid-button {
        padding: 10px 20px;
        font-size: 16px;
    }
}

/* High contrast mode for accessibility */
@media (prefers-contrast: high) {
    .vicino-auction-container {
        border: 2px solid #000000;
    }
    
    .vicino-auction-title {
        color: #000000;
    }
    
    .vicino-auction-active {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }
    
    .vicino-auction-inactive {
        background-color: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }
    
    .vicino-auction-price-info .price,
    .vicino-bid-history-table .bid-amount,
    .winning-bid {
        color: #000000;
    }
    
    .vicino-place-bid-button {
        background-color: #000000;
        color: #ffffff;
    }
    
    .vicino-place-bid-button:hover {
        background-color: #333333;
    }
    
    .vicino-login-message a {
        color: #000000;
    }
}

/* Time information styles */
.vicino-auction-time-info {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f0f7ff;
    border-radius: 8px;
    border: 1px solid #d0e1f9;
}

.vicino-auction-dates {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 15px;
}

.vicino-auction-start-date,
.vicino-auction-end-date {
    flex: 1;
    min-width: 250px;
    margin-bottom: 10px;
    font-size: 16px;
}

.vicino-auction-time-info .label {
    font-weight: bold;
    margin-right: 10px;
    color: #555555;
    display: inline-block;
    min-width: 140px;
}

.vicino-auction-time-info .date {
    font-weight: 500;
    color: #333333;
}

.vicino-time-extended {
    display: inline-block;
    margin-left: 10px;
    padding: 2px 8px;
    background-color: #fff3cd;
    color: #856404;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
}

.vicino-auction-countdown {
    padding: 20px;
    background: linear-gradient(135deg, #1a237e, #283593);
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    font-size: 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 25px auto;
    max-width: 500px;
    transition: all 0.3s ease;
}

.vicino-auction-countdown:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.25);
}

.vicino-auction-countdown .label {
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10px;
    font-size: 22px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.vicino-auction-countdown .countdown-timer {
    font-weight: bold;
    font-size: 32px;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 8px;
    display: inline-block;
    min-width: 200px;
}

.vicino-countdown-ending {
    background: linear-gradient(135deg, #c62828, #d32f2f);
    animation: pulse-urgent 1.5s infinite;
    border: 2px solid #ffcdd2;
}

.vicino-countdown-ending .label {
    color: #ffffff;
}

.vicino-countdown-ending .countdown-timer {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.2);
}

@keyframes pulse-urgent {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 0 10px rgba(255, 82, 82, 0);
        transform: scale(1.02);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 82, 82, 0);
        transform: scale(1);
    }
}

/* Styles for countdown in lot list view */
.vicino-auction-lot-time {
    text-align: center;
    margin: 15px 0;
}

.vicino-auction-lot-time .label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 16px;
    color: #555555;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vicino-auction-lot-time .countdown-timer {
    font-weight: bold;
    font-size: 20px;
    color: #ffffff;
    background: linear-gradient(135deg, #1a237e, #283593);
    padding: 8px 15px;
    border-radius: 8px;
    display: inline-block;
    margin-top: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-family: 'Courier New', monospace;
    transition: all 0.3s ease;
    min-width: 150px;
}

.vicino-auction-lot-time .countdown-timer:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.vicino-auction-lot-time .countdown-timer.vicino-countdown-ending {
    background: linear-gradient(135deg, #c62828, #d32f2f);
    animation: pulse-urgent 1.5s infinite;
}

.vicino-time-extended-badge {
    display: inline-block;
    margin-left: 8px;
    padding: 3px 8px;
    background-color: #fff3cd;
    color: #856404;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    vertical-align: middle;
}

/* Responsive adjustments for time info */
@media (max-width: 768px) {
    .vicino-auction-time-info {
        padding: 15px;
    }
    
    .vicino-auction-dates {
        flex-direction: column;
    }
    
    .vicino-auction-start-date,
    .vicino-auction-end-date {
        margin-bottom: 15px;
    }
    
    .vicino-auction-countdown {
        padding: 15px;
        font-size: 16px;
    }
    
    .vicino-auction-countdown .countdown-timer {
        font-size: 24px;
        min-width: 180px;
        padding: 8px 15px;
    }
    
    .vicino-auction-lot-time .countdown-timer {
        font-size: 16px;
        padding: 6px 10px;
    }
}

/* High contrast mode for time info */
@media (prefers-contrast: high) {
    .vicino-auction-time-info {
        background-color: #ffffff;
        border: 2px solid #000000;
    }
    
    .vicino-auction-time-info .label,
    .vicino-auction-time-info .date {
        color: #000000;
    }
    
    .vicino-time-extended {
        background-color: #ffffff;
        color: #000000;
        border: 1px solid #000000;
    }
    
    .vicino-auction-countdown {
        background-color: #ffffff;
        border: 2px solid #000000;
    }
    
    .vicino-auction-countdown .label,
    .vicino-auction-countdown .countdown-timer {
        color: #000000;
    }
}

/* Auction Results Table Styles */
.vicino-auction-results-container {
    margin-bottom: 40px;
    padding: 30px;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    font-size: 16px;
    max-width: 100%;
    overflow: hidden;
}

.vicino-auction-results-title {
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 28px;
    font-weight: 700;
    color: #333333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.vicino-auction-filter-container {
    margin-bottom: 25px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eeeeee;
}

.vicino-auction-filter-container label {
    display: inline-block;
    margin-right: 15px;
    font-weight: bold;
    font-size: 16px;
    color: #555555;
    vertical-align: middle;
}

.vicino-lot-filter {
    padding: 8px 12px;
    font-size: 16px;
    border: 2px solid #dddddd;
    border-radius: 6px;
    background-color: #ffffff;
    min-width: 200px;
    transition: border-color 0.3s;
}

.vicino-lot-filter:focus {
    border-color: #1976d2;
    outline: none;
}

.vicino-auction-export-container {
    margin-bottom: 25px;
    padding: 15px;
    background-color: #e3f2fd;
    border-radius: 8px;
    text-align: center;
}

.vicino-auction-results-table-container {
    overflow-x: auto;
    margin-bottom: 15px;
}

.vicino-auction-results-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

.vicino-auction-results-table th {
    background-color: #1976d2;
    color: #ffffff;
    font-weight: bold;
    text-align: left;
    padding: 15px 12px;
    border-bottom: 2px solid #1565c0;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vicino-auction-results-table td {
    padding: 12px;
    border-bottom: 1px solid #eeeeee;
    vertical-align: middle;
    font-size: 14px;
    height: 55px;
}

.vicino-auction-results-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.vicino-auction-results-table tr:hover {
    background-color: #f0f7ff;
    transition: background-color 0.2s;
}

.vicino-auction-results-table .bid-amount {
    font-weight: bold;
    color: #1976d2;
    text-align: right;
}

.vicino-auction-results-table .status-active {
    color: #2e7d32;
    font-weight: bold;
    background-color: #e8f5e9;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-transform: uppercase;
}

.vicino-auction-results-table .status-no-bids {
    color: #757575;
    font-weight: bold;
    background-color: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-transform: uppercase;
}

.vicino-auction-results-table .status-other {
    color: #ff8f00;
    font-weight: bold;
    background-color: #fff8e1;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    text-transform: uppercase;
}

.no-results-message {
    padding: 30px;
    background-color: #f5f5f5;
    border-radius: 8px;
    color: #757575;
    font-style: italic;
    text-align: center;
    font-size: 18px;
}

.no-filter-results td {
    padding: 20px;
    text-align: center;
    color: #757575;
    font-style: italic;
    background-color: #f9f9f9;
}

/* Responsive adjustments for results table */
@media (max-width: 1200px) {
    .vicino-auction-results-table {
        font-size: 13px;
    }

    .vicino-auction-results-table th,
    .vicino-auction-results-table td {
        padding: 10px 8px;
    }
}

@media (max-width: 768px) {
    .vicino-auction-results-container {
        padding: 20px;
    }

    .vicino-auction-results-title {
        font-size: 24px;
    }

    .vicino-auction-filter-container {
        padding: 15px;
    }

    .vicino-auction-filter-container label {
        display: block;
        margin-bottom: 10px;
        margin-right: 0;
    }

    .vicino-lot-filter {
        width: 100%;
        min-width: auto;
    }

    .vicino-auction-results-table {
        font-size: 12px;
    }

    .vicino-auction-results-table th,
    .vicino-auction-results-table td {
        padding: 8px 6px;
    }

    .vicino-auction-results-table th {
        font-size: 12px;
    }

    /* Hide less important columns on mobile */
    .vicino-auction-results-table .telefono,
    .vicino-auction-results-table .cuit {
        display: none;
    }

    .vicino-auction-results-table th:nth-child(5),
    .vicino-auction-results-table th:nth-child(6) {
        display: none;
    }
}

/* High contrast mode for results table */
@media (prefers-contrast: high) {
    .vicino-auction-results-container {
        border: 2px solid #000000;
    }

    .vicino-auction-results-title {
        color: #000000;
    }

    .vicino-auction-results-table th {
        background-color: #000000;
        color: #ffffff;
    }

    .vicino-auction-results-table .bid-amount {
        color: #000000;
    }

    .vicino-auction-results-table .status-active,
    .vicino-auction-results-table .status-no-bids,
    .vicino-auction-results-table .status-other {
        background-color: #ffffff;
        color: #000000;
        border: 1px solid #000000;
    }

    .vicino-lot-filter {
        border: 2px solid #000000;
        background-color: #ffffff;
        color: #000000;
    }
}