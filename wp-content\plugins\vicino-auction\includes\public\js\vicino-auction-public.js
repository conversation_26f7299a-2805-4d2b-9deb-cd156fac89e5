/**
 * All of the JavaScript for your public-facing functionality should be
 * included in this file.
 */
(function($) {
    'use strict';

    /**
     * Initialize public functionality when the DOM is ready
     */
    $(document).ready(function() {
        // The bid form submission is handled directly in the shortcode template
        // with inline JavaScript for simplicity
        
        // Frontend Export Button Handler
        $('#vicino-export-results-button').on('click', function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $status = $('#vicino-export-status');
            var nonce = $button.data('nonce');
            
            $button.prop('disabled', true);
            $status.text(vicino_auction_public.export_processing || 'Procesando exportación...'); // Add localization later if needed

            $.ajax({
                url: vicino_auction_public.ajax_url,
                type: 'POST',
                data: {
                    action: 'vicino_frontend_export', // Action hook for the PHP handler
                    nonce: nonce
                },
                success: function(response) {
                    if (response.success) {
                        $status.html('<a href="' + response.data.file_url + '" download>Descargar archivo</a>');
                    } else {
                        $status.text(response.data || vicino_auction_public.export_error || 'Error al exportar.'); // Add localization later if needed
                    }
                },
                error: function() {
                    $status.text(vicino_auction_public.export_error || 'Error al exportar.'); // Add localization later if needed
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        });

        // Any additional initialization code can go here
    });

})(jQuery);