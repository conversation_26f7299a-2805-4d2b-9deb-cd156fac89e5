<?php
/**
 * Plugin Name: Vicino Auction
 * Plugin URI: https://example.com/vicino-auction
 * Description: A WordPress plugin for managing lot-based auctions with pre-bidding, automatic time extension, and Excel export functionality.
 * Version: 2.0.1
 * Author: Vicino
 * Author URI: https://example.com
 * Text Domain: vicino-auction
 * Domain Path: /languages
 * Requires at least: 5.5
 * Requires PHP: 7.4
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('VICINO_AUCTION_VERSION', '1.0.0');
define('VICINO_AUCTION_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('VICINO_AUCTION_PLUGIN_URL', plugin_dir_url(__FILE__));

// Include required files
require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction.php';
require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-activator.php';
require_once VICINO_AUCTION_PLUGIN_DIR . 'includes/class-vicino-auction-deactivator.php';

// Register activation and deactivation hooks
register_activation_hook(__FILE__, array('Vicino_Auction_Activator', 'activate'));
register_deactivation_hook(__FILE__, array('Vicino_Auction_Deactivator', 'deactivate'));

/**
 * Begins execution of the plugin.
 */
function run_vicino_auction() {
    $plugin = new Vicino_Auction();
    $plugin->run();
}

// Start the plugin
run_vicino_auction();